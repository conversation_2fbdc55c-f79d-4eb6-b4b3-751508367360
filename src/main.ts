/// <reference types="@angular/localize" />

import { platformBrowserDynamic } from '@angular/platform-browser-dynamic';

import { AppModule } from './app/app.module';

// Check if body has the "data-istool" attribute
if (!document.body.hasAttribute('data-istool')) {
  // Only bootstrap the Angular application if we're not in editor mode
  platformBrowserDynamic().bootstrapModule(AppModule)
    .catch(err => console.error(err));
} else {
  console.log('Between embed script is disabled in editor mode');
}
