import {Injector, NgModule, LOCALE_ID, Do<PERSON><PERSON>strap, CUSTOM_ELEMENTS_SCHEMA} from '@angular/core';
import { BrowserModule } from '@angular/platform-browser';
import { AppComponent } from './app.component';

import {createCustomElement} from "@angular/elements";
import { NgbModule } from '@ng-bootstrap/ng-bootstrap';

import { IconModule } from './shared/icon.module';
import {AngularSvgIconModule} from "angular-svg-icon";
import {HttpClient, HttpClientModule} from "@angular/common/http";
import {NgOptimizedImage} from "@angular/common";
import {TranslateLoader, TranslateModule} from '@ngx-translate/core';
import {TranslateHttpLoader} from '@ngx-translate/http-loader';

import {FormsModule, ReactiveFormsModule} from "@angular/forms";
import {Select2Module} from "ng-select2-component";

import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { registerLocaleData } from '@angular/common';
import localeNb from '@angular/common/locales/nb';
import { NgOtpInputModule } from 'ng-otp-input';
import { LayoutComponent } from './layout/layout.component';
import { ChooseProductComponent } from './pages/choose-product/choose-product.component';
import { AddressesComponent } from './pages/addresses/addresses.component';
import { AddressInputComponent } from './pages/addresses/components/address-input/address-input.component';
import { FooterComponent } from './components/footer/footer.component';
import { CollapsibleItemComponent } from './components/footer/components/collapsible-item/collapsible-item.component';
import { OrderSummaryComponent } from './components/order-summary/order-summary.component';
import { SpecificationsComponent } from './pages/specifications/specifications.component';
import {InstructionsComponent} from "./pages/instructions/instructions.component";
import {CustomerInformationComponent} from "./pages/customer-information/customer-information.component";
import {DateAndTimeComponent} from "./pages/date-and-time/date-and-time.component";
import {ConfirmationComponent} from "./pages/confirmation/confirmation.component";
import {OptionsComponent} from "./pages/specifications/components/options/options.component";
import {ButtonLoaderComponent} from "./components/button-loader/button-loader.component";
import {UpsellProductsComponent} from "./pages/upsell-products/upsell-products.component";
import {ProductBoxComponent} from "./components/product-box/product-box.component";
import { MatExpansionModule } from '@angular/material/expansion';
import {MatTooltip} from "@angular/material/tooltip";
import {FinishedComponent} from "./pages/finished/finished.component";
import {RepeatingComponent} from "./pages/repeating/repeating.component";
import {OptionBoxComponent} from "./pages/repeating/components/option-box/option-box.component";
import {CustomOptionComponent} from "./pages/repeating/components/custom-option/custom-option.component";
import {SelectoriniComponent} from "./components/selectorini/selectorini.component";
import {SpinnerComponent} from "./components/spinner/spinner.component";


registerLocaleData(localeNb);

@NgModule({
  declarations: [
    AppComponent,
    LayoutComponent,
    ChooseProductComponent,
    AddressesComponent,
    AddressInputComponent,
    FooterComponent,
    CollapsibleItemComponent,
    OrderSummaryComponent,
    SpecificationsComponent,
    InstructionsComponent,
    CustomerInformationComponent,
    DateAndTimeComponent,
    ConfirmationComponent,
    OptionsComponent,
    ButtonLoaderComponent,
    UpsellProductsComponent,
    ProductBoxComponent,
    FinishedComponent,
    RepeatingComponent,
    CustomOptionComponent,
    SelectoriniComponent
  ],
  imports: [
    BrowserModule,
    NgbModule,
    IconModule,
    HttpClientModule,
    AngularSvgIconModule.forRoot(),
    NgOptimizedImage,
    TranslateModule.forRoot({
      loader: {
        provide: TranslateLoader,
        useFactory: HttpLoaderFactory,
        deps: [HttpClient]
      }
    }),
    ReactiveFormsModule,
    FormsModule,
    Select2Module,
    BrowserAnimationsModule,
    NgOtpInputModule,
    MatExpansionModule,
    MatTooltip,
    OptionBoxComponent,
    SpinnerComponent
  ],
  providers: [{provide: LOCALE_ID, useValue: 'nb-NO'}],
  exports: [
    SelectoriniComponent
  ],
  // bootstrap: [AppComponent], 
  schemas: [CUSTOM_ELEMENTS_SCHEMA]
})
export class AppModule implements DoBootstrap{
  constructor(private injector: Injector) {}

  ngDoBootstrap() {
    const element = createCustomElement(AppComponent, {injector: this.injector});
    customElements.define('between-embed', element)
  }
}

export function HttpLoaderFactory(http: HttpClient): TranslateHttpLoader {
  return new TranslateHttpLoader(http);
}
