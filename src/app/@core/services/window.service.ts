import { Injectable } from '@angular/core';
import { BehaviorSubject } from 'rxjs';

@Injectable({
    providedIn: 'root',
})
export class WindowService {
    private isMobile = new BehaviorSubject<boolean>(window.innerWidth <= 767);
    public isMobile$ = this.isMobile.asObservable();

    constructor() {
        // Check if body has the "data-istool" attribute
        const isEditorTool = document.body.hasAttribute('data-istool');

        // Only add event listener if we're not in editor mode
        if (!isEditorTool) {
            window.addEventListener('resize', () => {
                this.isMobile.next(window.innerWidth <= 767);
            });
        }
    }
}
