import { Injectable } from '@angular/core';
import { BehaviorSubject } from 'rxjs';

@Injectable({
    providedIn: 'root',
})
export class WindowService {
    private isMobile = new BehaviorSubject<boolean>(window.innerWidth <= 767);
    public isMobile$ = this.isMobile.asObservable();

    constructor() {
        window.addEventListener('resize', () => {
            this.isMobile.next(window.innerWidth <= 767);
        });
    }
}
