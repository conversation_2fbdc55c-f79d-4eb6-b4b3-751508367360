import {Component, Input, OnChanges, OnInit, SimpleChanges, Output, EventEmitter} from '@angular/core';
import {NgbActiveModal} from "@ng-bootstrap/ng-bootstrap";
import {
  AffiliateResponse,
  CompanyResponse,
  EmbedProductResponse,
  EmbedResponse,
  PriceCalculationResponse,
  PriceRuleResponse,
  ProductStageResponse, ScheduleRepeatTypeResponse,
  SpecificationResponse, SpecificationSelectedChoice,
  UpsellProductResponse
} from "../@core/models/responses.service";
import {EmbedService} from "../@core/services/embed.service";
import {
  AddressInput,
  CRM_PRD_30,
  ChildOrderlineStageInput,
  OrderLineStageInput,
  ProductCalculationStagesInput,
  TempOrder,
  CRM_EMB_4, USM_USR_2, CRM_CUS_1,
  CRM_AFF_12
} from "../@core/models/input.service";
import { StorageService } from 'src/app/@core/services/storage.service';
import { fas } from '@fortawesome/pro-solid-svg-icons'
import { far } from '@fortawesome/pro-regular-svg-icons'
import { fal } from '@fortawesome/pro-light-svg-icons'
import { FaIconLibrary } from '@fortawesome/angular-fontawesome';
import { trigger, transition, style, animate } from '@angular/animations';
import { WindowService } from 'src/app/@core/services/window.service';
import {calculateBrightness} from "../@core/utils/utils-functions";

export interface OrderLineAddressStage extends OrderLineStageInput, ProductStageResponse {
  childStages: OrderLineAddressStage[];
  tempId: number;
}

export interface TempOrderLineStage extends OrderLineStageInput {
  tempId: number;
  hasAddress?: boolean;
}

@Component({
  selector: 'app-layout',
  templateUrl: './layout.component.html',
  styleUrls: ['./layout.component.css'],
  animations: [
    trigger('fadeAnimation', [
      transition(':enter', [
        style({ opacity: 0.8, transform: 'translateY(-5px)' }),
        animate('0.3s ease-out', style({ opacity: 1, transform: 'translateY(0px)' }))
      ]),
    ]),
    trigger('growBounce', [
      transition(':enter', [
        style({ transform: 'scale(0.95)', opacity: 0.8 }),
        animate('0.3s cubic-bezier(0.68, -0.55, 0.27, 1.55)', style({ transform: 'scale(1)', opacity: 1 }))
      ])
    ])
  ]
})


export class LayoutComponent implements OnInit, OnChanges{
  @Input() companyId: string;
  @Input() embedId: number;
  @Input() header: string;
  @Input() stage_name: string;
  @Input() themeColor: string;
  @Output() isMobileChange = new EventEmitter<boolean>();
  pages = [
    'product',
    'address',
    'date_and_time',
    'customer_information',
    'confirmation',
    'finished'
  ];


  company: CompanyResponse;
  embed: EmbedResponse;
  embedProducts: EmbedProductResponse[] = [];
  embedSpecifications: SpecificationResponse[] = [];

  order: TempOrder;
  selectedProduct: EmbedProductResponse;
  upsellServiceProducts : UpsellProductResponse[] = [];
  upsellStandardProducts: UpsellProductResponse[] = [];
  allUpsellProducts: UpsellProductResponse[] = [];
  specifications: SpecificationResponse[] = [];

  activeView: string = 'product';


  storageOrder = null;
  isLoading: boolean = false;
  priceCalculation: PriceCalculationResponse;
  addressStages: OrderLineAddressStage[] = [];
  tempOrderLineStages: TempOrderLineStage[] = [];
  childLoaded: boolean = false;
  isMobile: boolean;
  selectedProductStages: ProductCalculationStagesInput[] = [];
  extraStageAddresses: ChildOrderlineStageInput[] = [];
  continueBtnState: boolean = true;
  updatingOrder: number = 0;
  showKvmTooltip: boolean = false;
  priceCalculationOngoing: boolean = false;
  orderConfirmed: boolean = false;
  scheduleOptions: ScheduleRepeatTypeResponse[]
  google_tracking_id: string;

  constructor(public activeModal: NgbActiveModal,
              private storageService: StorageService,
              library: FaIconLibrary,
              private embedService: EmbedService,
              private windowService: WindowService,
              @Inject(DOCUMENT) private document: Document) {

    library.addIconPacks(far, fas, fal);

  }

  ngOnInit() {
    this.getCompanyData()
    this.getEmbed(this.embedId);

    this.windowService.isMobile$.subscribe((isMobile) => {
      this.isMobile = isMobile;
      this.isMobileChange.emit(this.isMobile);
    });
    // this.initializeGoogleAnalytics();

    this.resetOrder();

    // Marketing enabled ?
    if (this.embed){
      if (this.embed.google_tracking_id){
        this.loadAndInitGtag();
        this.google_tracking_id = this.embed.google_tracking_id;
      }
    }
    else {
      setTimeout(() => {
        if (this.embed?.google_tracking_id){
          this.loadAndInitGtag();
        }
      },500)
    }
  }

  getEmbed(embedId: number){
    let params: CRM_EMB_4 = {
      embed_id: embedId,
      company_id: this.companyId
    }
    this.embedService.getEmbed(params).subscribe((res: EmbedResponse) => {
      this.embed = res;
      this.embedProducts = res.embed_products.sort((a, b) => a.index - b.index);
    })
  }

  closeIframe() {
    var modal = document.querySelector('.custom-class');
    modal!.classList.remove('show');
    modal!.classList.add('hide');
    // Check if it's in an iframe
    if (window.self !== window.top) {
      // We are in an iframe, so post a message to the parent
      window.parent.postMessage('closeIframe', '*');
    } else {
      // Not in an iframe, use NgbActiveModal to close
      this.activeModal.close('Cross click');
    }
  }

  ngOnChanges(changes: SimpleChanges): void{
    this.getCompanyData()
  }


  checkContinueBtnState(){
    if (this.activeView === 'address'){
      // For address validation, we'll be lenient
      // As long as there's at least one valid address in any of the stage arrays, we'll allow continuing

      // Check if there's at least one valid address in the order stages
      const hasValidOrderStage = this.order.stages.some(stage =>
        stage.address != null &&
        (stage.address.street != null || stage.address.display != null)
      );

      // Check if there's at least one valid address in the extra stages
      const hasValidExtraStage = this.extraStageAddresses.some(stage =>
        stage.address != null &&
        (stage.address.street != null || stage.address.display != null)
      );

      // Check if there's at least one valid address in the selected product stages
      const hasValidSelectedStage = this.selectedProductStages.some(stage =>
        stage.address != null &&
        (stage.address.street != null || stage.address.display != null)
      );

      // If any of the stage arrays has a valid address, allow continuing
      return hasValidOrderStage || hasValidExtraStage || hasValidSelectedStage;
    }
    else if(this.activeView === 'date_and_time'){
      // Allow continuing if date is selected or explicitly skipped (undefined)
      // Only block if selected_date_and_time is null (initial state)
      return this.order.selected_date_and_time !== null;
    }
    else if (this.activeView === 'customer_information') {
      // Validate email and phone formats (common for both private and company)
      const emailPattern = /^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,4}$/;
      const phonePattern = /^\d{8}$/; // Expecting 8 digits

      const isEmailValid = emailPattern.test(this.order.customer_email ? this.order.customer_email : '');
      const isPhoneValid = phonePattern.test(this.order.customer_phone ? this.order.customer_phone : '');

      // If basic validation fails, return false
      if (!isEmailValid || !isPhoneValid || !this.order.customer_email || !this.order.customer_phone) {
        return false;
      }

      // Check if it's a private customer or company
      if (this.order.is_private === 1) {
        // For private customers, check first and last name
        return !!this.order.customer_first_name && !!this.order.customer_last_name;
      } else {
        // For company customers, check company name, organization number, and contact person (first and last name)
        const orgNumberPattern = /^\d{9}$/; // Norwegian organization numbers are 9 digits
        const isValidOrgNumber = orgNumberPattern.test(this.order.customer_organisation_number || '');

        return !!this.order.customer_company_name &&
               !!this.order.customer_organisation_number &&
               isValidOrgNumber
      }
    }
    return true;
  }

  getCompanyData() {
    this.embedService.getCompanyData(this.companyId).subscribe((res: CompanyResponse) => {
      this.company = res
    })
  }

  fetchStorage(){
    this.order = this.storageService.get('order');
  }

  fetchUpsellProducts(){
    const params : CRM_PRD_30 = {
      product_id : this.order.product_id,
      company_id : this.companyId,
    }

    this.embedService.fetchUpsellProducts(params).subscribe(res => {
      this.allUpsellProducts = res;

      this.upsellServiceProducts = res.filter((product: UpsellProductResponse) => product.product_type_id === 1);
      this.upsellServiceProducts.sort((a: UpsellProductResponse, b: UpsellProductResponse ) => a.product_id - b.product_id);
      this.upsellStandardProducts = res.filter((product: UpsellProductResponse) => product.product_type_id === 2);
      this.upsellStandardProducts.sort((a: UpsellProductResponse, b: UpsellProductResponse ) => a.product_id - b.product_id);

      if (this.upsellServiceProducts.length > 0 && this.embed.show_upsell) {
        this.pages.splice(2, 0, 'upsell_products_services');
      }
      if (this.upsellStandardProducts.length > 0 && this.embed.show_upsell) {
        this.pages.splice(3, 0, 'upsell_products_standard');
      }
    })
  }

  resetOrder(){
    this.selectedProductStages = [];

    this.order = {
      product_id : 0,
      quantity : 1,
      product_unit : 0,
      product_name: '',
      fixed_price : 0,
      hourly_rate : 0,
      price: 0,
      stages: [],
      selectedPriceRules: [],
      date_and_time: new Date(),
      selected_date_and_time: null,
      specifications: [],
      instructions: null,
      affiliate_contact: null,
      customer_id: null,
      customer_first_name : null,
      customer_last_name: null,
      customer_full_name: null,
      customer_email: null,
      customer_phone: null,
      customer_company_name: null,
      customer_organisation_number: null,
      is_private: 1, // Default to private customer
      unit_id: 0,
      price_calculations: null,
      upsell_products: [],
      scheduleInput: null,
    };

    this.extraStageAddresses = [];
  }
  handleSelectProductEvent(value: EmbedProductResponse) {
    this.isLoading = true;
    this.resetOrder();
    this.upsellServiceProducts = [];
    this.upsellStandardProducts = [];
    this.pages = [
      'product',
      'address',
      'date_and_time',
      'customer_information',
      'confirmation',
      'finished'
    ];

    this.scheduleOptions = value.order_schedule_templates
    if (value.order_schedule_templates.length > 0) {
      this.pages.splice(this.pages.indexOf('date_and_time') + 1, 0, `repeating`);
    }

    this.selectedProduct = value;
    this.specifications = value.specifications.sort((a, b) => a.index - b.index);
    if (this.selectedProduct.specifications.length > 0) {
      let count = 0
      this.selectedProduct.specifications.forEach(() => {
        if (value.order_schedule_templates.length > 0){
          this.pages.splice(this.pages.indexOf('date_and_time') + 2 + count, 0, `specifications`);
          count++;
        }
        else {
          this.pages.splice(this.pages.indexOf('date_and_time') + 1 + count, 0, `specifications`);
          count++;
        }
      });
    }

    this.activeView = 'address';

    this.order.quantity = 1;
    this.order.product_id = value.product_id;
    this.order.product_name = value.product_name;
    this.order.product_unit = value.unit_id;
    this.order.price = value.price_ex_vat;
    this.order.unit_id = value.unit_id;
    this.order.hourly_rate = 0;
    this.order.fixed_price = 0;

    this.storageService.set('order', this.order);
    this.isLoading = false;

    this.selectedProduct.stages.map((stage) => {
      const stageInput : ProductCalculationStagesInput = {
        stage_id : stage.stage_id,
        address: null,
      }

      this.selectedProductStages.push(stageInput);
    })

    this.fetchUpsellProducts();
  }

  handleTempStageUpdate(stages: TempOrderLineStage[]) {
    this.tempOrderLineStages = stages;
  }

  pdateOrder(key: string, value: any){
    this.storageService.set(`order.${key}`, value);
  }

  getNewTempId() {
    let tempId = 0;
    for (const stage of this.tempOrderLineStages) {
      if (stage.tempId > tempId) {
        tempId = stage.tempId;
      }
    }
    return tempId + 1;
  }

  addAddressStage(stage: OrderLineAddressStage) {
    const orderLineAddressStage: OrderLineAddressStage = {
      ...stage,
      stage_id: stage.stage_id,
      stage_name: stage.stage_name,
      address: null,
      sets_quantity: 0,
      tempId: this.getNewTempId()
    }
    this.addressStages.find(x => x.tempId == stage.tempId)?.childStages.push(orderLineAddressStage);
  }

  handleContinueClick(){
    if (this.activeView === 'customer_information') {
      // Check if it's a private customer or company
      if (this.order.is_private === 1) {
        // For private customers, use the existing endpoint
        let params: CRM_CUS_1 = {
          company_id: this.companyId,
          first_name: this.order.customer_first_name!,
          last_name: this.order.customer_last_name!,
          email: this.order.customer_email!,
          phone: `+47${this.order.customer_phone!}`,
          is_private: 1
        }

        this.embedService.createCustomer(params).subscribe({
          next: (data) => {
            this.order.customer_id = data.affiliate_id;
            this.order.customer_full_name = data.name;
            this.order.customer_email = data.email;
            this.order.customer_phone = data.phone.substring(3);

            // Force change detection by creating a new object
            this.order = {...this.order};

            const activeIndex = this.pages.indexOf(this.activeView);
            if (activeIndex !== -1 && activeIndex < this.pages.length - 1) {
              this.activeView = this.pages[activeIndex + 1];
            }
          },
          error: (error) => {
            console.error('Error creating private customer:', error);
            alert('Error creating private customer. Please try again.');
          }
        });
      } else {
        // For company customers, use the new endpoint
        let params: CRM_AFF_12 = {
          company_id: this.companyId,
          company_name: this.order.customer_company_name!,
          phone: null,
          email: null,
          organisation_number: this.order.customer_organisation_number!,
          company_type_id: 'private_company_localised',
          affiliate_contact: {
            first_name: this.order.customer_first_name!,
            last_name: this.order.customer_last_name!,
            phone: `+47${this.order.customer_phone!}`,
            email: this.order.customer_email!
          }
        }

        this.embedService.createCompanyCustomer(params).subscribe((data: AffiliateResponse) => {
          // Update the order with the response data
          this.order.customer_id = data.affiliate_id;
          this.order.customer_full_name = data.name;
          this.order.affiliate_contact = data.affiliate_contact;

          // Move to the next page - this is the key part
          const activeIndex = this.pages.indexOf(this.activeView);
          if (activeIndex !== -1 && activeIndex < this.pages.length - 1) {
            // Use handleViewChange instead of directly setting activeView
            // This ensures proper view transition
            this.handleViewChange(this.pages[activeIndex + 1]);
          }
        });
      }
    }
    else {
      // For all other views, including address view
      const activeIndex = this.pages.indexOf(this.activeView);
      if (activeIndex !== -1 && activeIndex < this.pages.length - 1) {
        const nextView = this.pages[activeIndex + 1];
        this.activeView = nextView;
      }
    }
  }

  handleBackClick(){
    const activeIndex = this.pages.indexOf(this.activeView);
    this.activeView = this.pages[activeIndex - 1];
  }

  handleUpsellProductUpdate(_upsellProducts: UpsellProductResponse[]) {
    this.updatingOrder++;
  }

  /**
   * Skip date selection and proceed to the next step
   * Sets a flag in the order to indicate that date selection was skipped
   */
  skipDateSelection() {
    // Set selected_date_and_time to a special value to indicate it was skipped
    this.order.selected_date_and_time = undefined;

    // Continue to the next page
    this.handleContinueClick();
  }

  handleViewChange(view: any) {
    this.activeView = view;
  }

  handleOrderUpdate(updatedOrder: TempOrder){
    this.order = updatedOrder;
    this.updatingOrder++;
  }

  handleSpecificationUpdate(data: any) {
    let updatedSpecification: SpecificationSelectedChoice = data.data;
    let index = data.index;
    if (index !== -1) {
      this.order.specifications[index].choice = updatedSpecification.choice;
    } else {
      this.order.specifications.push(updatedSpecification);
    }
    this.order = {...this.order};
  }

  getProgressPercentage(): number {
    const activeIndex = this.pages.indexOf(this.activeView);
    const progressPercentage = ((activeIndex / (this.pages.length - 1)) * 90) + 10;
    return progressPercentage;
  }


  getDarkerColor(): string {
    const r = parseInt(this.themeColor.slice(1, 3), 16);
    const g = parseInt(this.themeColor.slice(3, 5), 16);
    const b = parseInt(this.themeColor.slice(5, 7), 16);

    const darkenedR = Math.floor(r * 0.85).toString(16).padStart(2, '0');
    const darkenedG = Math.floor(g * 0.85).toString(16).padStart(2, '0');
    const darkenedB = Math.floor(b * 0.85).toString(16).padStart(2, '0');

    return `#${darkenedR}${darkenedG}${darkenedB}`;
  }

  getLighterColor(): string {
    // We don't need to use the rgb object here, so we can directly parse the hex values
    const r = parseInt(this.themeColor.slice(1, 3), 16);
    const g = parseInt(this.themeColor.slice(3, 5), 16);
    const b = parseInt(this.themeColor.slice(5, 7), 16);

    const lightenedR = Math.min(255, Math.floor(r * 1.15)).toString(16).padStart(2, '0');
    const lightenedG = Math.min(255, Math.floor(g * 1.15)).toString(16).padStart(2, '0');
    const lightenedB = Math.min(255, Math.floor(b * 1.15)).toString(16).padStart(2, '0');
    return `#${lightenedR}${lightenedG}${lightenedB}`;
  }

  hexToRgb(hex: string): { r: number; g: number; b: number } | null {
    // Remove the hash if it's included
    hex = hex.replace(/^#/, '');

    // Parse the hex values
    const bigint = parseInt(hex, 16);

    // Extract RGB components
    const r = (bigint >> 16) & 255;
    const g = (bigint >> 8) & 255;
    const b = bigint & 255;

    // Return as an object
    return { r, g, b };
  }


  onLoaded(isLoaded: boolean) {
    if (isLoaded) {
      // Use setTimeout to push this change to the next change detection cycle
      setTimeout(() => {
        this.childLoaded = true;
      });
    }
  }

  handlePriceCalculationOngoing(ongoing: boolean) {
    this.priceCalculationOngoing = ongoing;
  }

  goToLink(url: string){
    window.open(url, "_blank");
  }

  private loadAndInitGtag(): void {
    // Don't proceed if tracking ID is missing
    if (!this.embed?.google_tracking_id) {
      console.warn('Google Analytics tracking ID is missing');
      return;
    }

    // If gtag is already initialized with our tracking ID, don't reinitialize
    if (window.dataLayer && typeof window.gtag === 'function') {
      try {
        // Add our config if not already present
        window.gtag('config', this.embed.google_tracking_id, { 'send_page_view': false });
      } catch (error) {
        console.error('Error configuring Google Analytics:', error);
      }
      return;
    }

    try {
      // Initialize dataLayer first to avoid 'r' variable redeclaration
      window.dataLayer = window.dataLayer || [];
      window.gtag = function() { window.dataLayer.push(arguments); };
      window.gtag('js', new Date());

      // Create and load the gtag script
      const script = document.createElement('script');
      script.async = true;
      script.src = 'https://www.googletagmanager.com/gtag/js?id=' + this.embed.google_tracking_id;

      script.onload = () => {
        // Just configure the tracking ID, don't reinitialize gtag
        window.gtag('config', this.embed.google_tracking_id, { 'send_page_view': false });
        console.log('Google Analytics initialized with ID:', this.embed.google_tracking_id);
      };

      script.onerror = (error) => {
        console.error('Error loading Google Analytics script:', error);
      };

      document.head.appendChild(script);
    } catch (error) {
      console.error('Error setting up Google Analytics:', error);
    }
  }



  protected readonly calculateBrightness = calculateBrightness;
}

