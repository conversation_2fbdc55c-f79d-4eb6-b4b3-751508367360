import {
  Component,
  HostListener,
  Inject,
  Injector,
  Input,
  NgZone,
  OnChanges,
  OnInit,
  Renderer2,
  SimpleChanges,
  ViewEncapsulation
} from '@angular/core';
import {NgbModal, NgbModalRef} from "@ng-bootstrap/ng-bootstrap";
import {TranslateService} from "@ngx-translate/core";
import {LocalizationService} from "./@core/services/localization.service";
import {LayoutComponent} from "./layout/layout.component";
import {CRM_EMB_4} from "./@core/models/input.service";
import {EmbedResponse} from "./@core/models/responses.service";
import {EmbedService} from "./@core/services/embed.service";
import { DOCUMENT } from '@angular/common';

@Component({
  selector: 'app-root',
  templateUrl: './app.component.html',
  styleUrls: ['./app.component.css'],
  encapsulation: ViewEncapsulation.Emulated
})
export class AppComponent implements OnInit{
  @Input() companyid: string = 'COY-7';
  @Input() embedid: number = 24;
  @Input() buttonbackgroundcolor: string = '#000000';
  @Input() buttonbordercolor: string = '#000000';
  @Input() buttontextcolor: string = '#FFFFFF';
  @Input() buttonhovertextcolor: string = '#000000';
  @Input() buttonhoverbackgroundcolor: string = '#000000';
  @Input() buttonhoverbordercolor: string = '#FFFFFF';
  @Input() buttonborderradius: number = 5;
  @Input() themecolor: string = '#0000ff';
  @Input() header: string = 'Between';
  @Input() language: string = 'no';

  // @Input() companyid: string = 'COY-1';
  // @Input() embedid: number;
  // @Input() buttonbackgroundcolor: string;
  // @Input() buttonbordercolor: string;
  // @Input() buttontextcolor: string;
  // @Input() buttonhoverbackgroundcolor: string;
  // @Input() buttonhoverbordercolor: string;
  // @Input() buttonborderradius: number
  // @Input() themecolor: string;
  // @Input() header: string;
  // @Input() language: string = 'no';
  public isButtonVisible: boolean = true;

  modalReference: NgbModalRef;

  constructor(private modalService: NgbModal,
              private injector: Injector,
              private translate: TranslateService,
              private localization: LocalizationService,
              private zone: NgZone,
              private renderer: Renderer2,
               @Inject(DOCUMENT) private document: Document,
              private embedService: EmbedService) {localization.use(this.language);}

  ngOnInit() {
    if (this.embedid && this.companyid) {
      this.getEmbed(this.embedid);
      // Add event listener for window resize
      window.addEventListener('resize', this.adjustModalClass.bind(this));
      window.addEventListener('message', this.handleMessage.bind(this));
      this.setRootFontSize('16px');
      console.log('Between embed script is initialized');
    }
  }

  setRootFontSize(size: string) {
    this.renderer.setStyle(this.document.documentElement, 'font-size', size);
  }

  getEmbed(embedid: number){
    let params: CRM_EMB_4 = {
      embed_id: embedid,
      company_id: this.companyid
    }
    this.embedService.getEmbed(params).subscribe((res: EmbedResponse) => {
      this.buttonbackgroundcolor = res.button_background_color;
      this.buttonbordercolor = res.button_border_color;
      this.buttontextcolor = res.button_text_color;
      this.buttonhovertextcolor = res.button_hover_text_color;
      this.buttonhoverbackgroundcolor = res.button_hover_background_color;
      this.buttonhoverbordercolor = res.button_hover_border_color;
      this.buttonborderradius = res.button_border_radius;
      this.header = res.button_text
      this.themecolor = res.primary_color

      document.documentElement.style.setProperty('--theme-color', this.themecolor);
      document.documentElement.style.setProperty('--button-background-color', this.buttonbackgroundcolor);
      document.documentElement.style.setProperty('--button-border-color', this.buttonbordercolor);
      document.documentElement.style.setProperty('--button-text-color', this.buttontextcolor);
      document.documentElement.style.setProperty('--button-hover-text-color', this.buttonhovertextcolor);
      document.documentElement.style.setProperty('--button-hover-background-color', this.buttonhoverbackgroundcolor);
      document.documentElement.style.setProperty('--button-hover-border-color', this.buttonhoverbordercolor);
      document.documentElement.style.setProperty('--button-border-radius', this.buttonborderradius + 'px');
    })
  }


  setThemeColor(color: string) {
    document.documentElement.style.setProperty('--theme-color', color);
  }

  @HostListener('window:message', ['$event'])
  handleMessage(event: MessageEvent) {
    // Adjust the origin check to match the origin from which you expect to receive the message
    if ((event.origin === 'https://dev.between.as' || event.origin === 'http://localhost:4200' || event.origin === 'https://between.as') && event.data === 'openWizardModal') {
      this.openWizardModal();
      this.isButtonVisible = false;
    }
  }


  openWizardModal() {
    if (this.modalReference) {
      this.modalReference.close();
    }
    this.modalReference = this.modalService.open(LayoutComponent, {
      centered: true,
      size: 'lg',
      backdrop: 'static',
      windowClass: 'custom-class',
      keyboard: false
    });
    this.modalReference.componentInstance.companyId = this.companyid;
    this.modalReference.componentInstance.embedId = this.embedid;
    this.modalReference.componentInstance.header = this.header;
    this.modalReference.componentInstance.themeColor = this.themecolor;

    // Adjust the modal class after opening
    this.adjustModalClass();

    // Remove event listener when modal is closed
    this.modalReference.result.then(
      () => {
        window.removeEventListener('resize', this.adjustModalClass.bind(this));
      }
    )
  }


  adjustModalClass() {
    if (!this.modalReference) return;

    // Assuming you have only one modal open at a time, if you can have multiple modals open you might want to adjust the selector
    const modalElement = document.querySelector('.modal-dialog');

    if (window.innerWidth < 768 && modalElement) {
      modalElement.classList.remove('modal-dialog-centered', 'modal-lg');
      modalElement.classList.add('modal-fullscreen-md-down');
    } else if (modalElement) {
      modalElement.classList.add('modal-dialog-centered', 'modal-lg');
      modalElement.classList.remove('modal-fullscreen-md-down');
    }
  }


  ngOnDestroy() {
    window.removeEventListener('resize', this.adjustModalClass);
    window.removeEventListener('message', this.handleMessage);
  }

}
